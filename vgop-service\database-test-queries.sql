-- 数据库表结构验证SQL查询
-- 用于开发环境调试和测试

-- 1. 检查systables系统表（Informix数据库）
SELECT tabname FROM systables WHERE tabtype = 'T' AND tabname LIKE '%mcn%' OR tabname LIKE '%vgop%';

-- 2. 检查mcn_user_major表是否存在及其结构
SELECT FIRST 1 * FROM mcn_user_major;

-- 3. 检查vgop_metrics_history表是否存在及其结构
SELECT FIRST 1 * FROM vgop_metrics_history;

-- 4. 查看mcn_user_major表的列信息
SELECT colname, coltype FROM syscolumns 
WHERE tabid = (SELECT tabid FROM systables WHERE tabname = 'mcn_user_major')
ORDER BY colno;

-- 5. 查看vgop_metrics_history表的列信息
SELECT colname, coltype FROM syscolumns 
WHERE tabid = (SELECT tabid FROM systables WHERE tabname = 'vgop_metrics_history')
ORDER BY colno;

-- 6. 测试简单查询 - mcn_user_major
SELECT COUNT(*) as total_users FROM mcn_user_major;

-- 7. 测试简单查询 - vgop_metrics_history
SELECT COUNT(*) as total_metrics FROM vgop_metrics_history;

-- 8. 查看mcn_user_major表的数据示例
SELECT phonenumber, phonestate, locationid 
FROM mcn_user_major 
WHERE phonestate IN ('0','1') 
LIMIT 5;

-- 9. 查看当前数据库名称
SELECT USER AS current_user, SITENAME AS database_name FROM systables WHERE tabid = 1; 