package com.vgop.service.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 告警信息实体类
 * 对应数据库表：vgop_validation_alerts
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidationAlert {
    
    /**
     * 告警ID
     */
    private Long alertId;
    
    /**
     * 告警时间
     */
    private LocalDateTime alertTime;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 告警类型
     */
    private String alertType;
    
    /**
     * 告警级别
     */
    private AlertLevel alertLevel;
    
    /**
     * 告警信息
     */
    private String alertMessage;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 行号
     */
    private Long lineNumber;
    
    /**
     * 错误数据
     */
    private String errorData;
    
    /**
     * 字段错误详情
     */
    private String fieldErrors;
    
    /**
     * 指标详情
     */
    private String metricDetails;
    
    /**
     * Excel报告路径
     */
    private String excelReportPath;
    
    /**
     * 状态
     */
    private AlertStatus status;
    
    /**
     * 处理人
     */
    private String handledBy;
    
    /**
     * 处理时间
     */
    private LocalDateTime handledTime;
    
    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        INFO("信息"),
        WARNING("警告"),
        ERROR("错误"),
        CRITICAL("严重");
        
        private final String description;
        
        AlertLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 告警状态枚举
     */
    public enum AlertStatus {
        NEW("新建"),
        ACKNOWLEDGED("已确认"),
        PROCESSING("处理中"),
        RESOLVED("已解决"),
        CLOSED("已关闭");
        
        private final String description;
        
        AlertStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 