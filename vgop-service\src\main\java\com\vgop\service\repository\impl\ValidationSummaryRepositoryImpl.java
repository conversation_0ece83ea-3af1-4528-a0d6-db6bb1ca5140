package com.vgop.service.repository.impl;

import com.vgop.service.repository.ValidationSummaryRepository;
import com.vgop.service.validation.ValidationSummary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验摘要仓库实现类
 */
@Repository
public class ValidationSummaryRepositoryImpl implements ValidationSummaryRepository {
    
    /**
     * JDBC模板
     */
    private final JdbcTemplate jdbcTemplate;
    
    /**
     * 校验摘要行映射器
     */
    private final RowMapper<ValidationSummary> rowMapper = (rs, rowNum) -> {
        // 解析JSON字段
        String severityCountsJson = rs.getString("severity_counts");
        String ruleCountsJson = rs.getString("rule_counts");
        String fieldCountsJson = rs.getString("field_counts");
        
        Map<String, Long> severityCounts = parseJsonToMap(severityCountsJson);
        Map<String, Long> ruleCounts = parseJsonToMap(ruleCountsJson);
        Map<String, Long> fieldCounts = parseJsonToMap(fieldCountsJson);
        
        return ValidationSummary.builder()
                .id(rs.getLong("id"))
                .interfaceName(rs.getString("interface_name"))
                .fileName(rs.getString("file_name"))
                .dataDate(rs.getString("data_date"))
                .totalRows(rs.getLong("total_rows"))
                .errorRows(rs.getLong("error_rows"))
                .totalErrors(rs.getLong("total_errors"))
                .errorRate(rs.getDouble("error_rate"))
                .severityCounts(severityCounts)
                .ruleCounts(ruleCounts)
                .fieldCounts(fieldCounts)
                .startTime(rs.getTimestamp("start_time").toLocalDateTime())
                .endTime(rs.getTimestamp("end_time") != null ? 
                        rs.getTimestamp("end_time").toLocalDateTime() : null)
                .duration(rs.getLong("duration"))
                .status(rs.getString("status"))
                .build();
    };
    
    @Autowired
    public ValidationSummaryRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    @Override
    public ValidationSummary save(ValidationSummary summary) {
        // 检查是否已存在
        ValidationSummary existingSummary = findByInterfaceNameAndFileNameAndDataDate(
                summary.getInterfaceName(), summary.getFileName(), summary.getDataDate());
        
        if (existingSummary != null) {
            // 更新现有记录
            jdbcTemplate.update(
                    "UPDATE vgop_validation_summaries SET " +
                    "total_rows = ?, error_rows = ?, total_errors = ?, error_rate = ?, " +
                    "severity_counts = ?, rule_counts = ?, field_counts = ?, " +
                    "start_time = ?, end_time = ?, duration = ?, status = ? " +
                    "WHERE id = ?",
                    summary.getTotalRows(),
                    summary.getErrorRows(),
                    summary.getTotalErrors(),
                    summary.getErrorRate(),
                    mapToJson(summary.getSeverityCounts()),
                    mapToJson(summary.getRuleCounts()),
                    mapToJson(summary.getFieldCounts()),
                    Timestamp.valueOf(summary.getStartTime()),
                    summary.getEndTime() != null ? Timestamp.valueOf(summary.getEndTime()) : null,
                    summary.getDuration(),
                    summary.getStatus(),
                    existingSummary.getId());
            
            summary.setId(existingSummary.getId());
            return summary;
        }
        
        // 插入新记录
        KeyHolder keyHolder = new GeneratedKeyHolder();
        
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(
                    "INSERT INTO vgop_validation_summaries (interface_name, file_name, data_date, " +
                    "total_rows, error_rows, total_errors, error_rate, " +
                    "severity_counts, rule_counts, field_counts, " +
                    "start_time, end_time, duration, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    Statement.RETURN_GENERATED_KEYS);
            
            ps.setString(1, summary.getInterfaceName());
            ps.setString(2, summary.getFileName());
            ps.setString(3, summary.getDataDate());
            ps.setLong(4, summary.getTotalRows());
            ps.setLong(5, summary.getErrorRows());
            ps.setLong(6, summary.getTotalErrors());
            ps.setDouble(7, summary.getErrorRate());
            ps.setString(8, mapToJson(summary.getSeverityCounts()));
            ps.setString(9, mapToJson(summary.getRuleCounts()));
            ps.setString(10, mapToJson(summary.getFieldCounts()));
            ps.setTimestamp(11, Timestamp.valueOf(summary.getStartTime()));
            ps.setTimestamp(12, summary.getEndTime() != null ? 
                    Timestamp.valueOf(summary.getEndTime()) : null);
            ps.setLong(13, summary.getDuration() != null ? summary.getDuration() : 0);
            ps.setString(14, summary.getStatus());
            
            return ps;
        }, keyHolder);
        
        Number key = keyHolder.getKey();
        if (key != null) {
            summary.setId(key.longValue());
        }
        
        return summary;
    }
    
    @Override
    public ValidationSummary findByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate) {
        
        List<ValidationSummary> results = jdbcTemplate.query(
                "SELECT * FROM vgop_validation_summaries " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ?",
                rowMapper,
                interfaceName, fileName, dataDate);
        
        return results.isEmpty() ? null : results.get(0);
    }
    
    @Override
    public List<ValidationSummary> findByInterfaceName(String interfaceName) {
        return jdbcTemplate.query(
                "SELECT * FROM vgop_validation_summaries " +
                "WHERE interface_name = ? " +
                "ORDER BY start_time DESC",
                rowMapper,
                interfaceName);
    }
    
    @Override
    public List<ValidationSummary> findRecentSummaries(int limit) {
        return jdbcTemplate.query(
                "SELECT * FROM vgop_validation_summaries " +
                "ORDER BY start_time DESC " +
                "LIMIT ?",
                rowMapper,
                limit);
    }
    
    @Override
    public int deleteByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate) {
        
        return jdbcTemplate.update(
                "DELETE FROM vgop_validation_summaries " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ?",
                interfaceName, fileName, dataDate);
    }
    
    /**
     * 将Map转换为JSON字符串
     * 
     * @param map Map对象
     * @return JSON字符串
     */
    private String mapToJson(Map<String, Long> map) {
        if (map == null || map.isEmpty()) {
            return "{}";
        }
        
        StringBuilder sb = new StringBuilder("{");
        boolean first = true;
        
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            if (!first) {
                sb.append(",");
            }
            sb.append("\"").append(entry.getKey()).append("\":")
              .append(entry.getValue());
            first = false;
        }
        
        sb.append("}");
        return sb.toString();
    }
    
    /**
     * 将JSON字符串解析为Map
     * 
     * @param json JSON字符串
     * @return Map对象
     */
    private Map<String, Long> parseJsonToMap(String json) {
        Map<String, Long> result = new HashMap<>();
        
        if (json == null || json.isEmpty() || json.equals("{}")) {
            return result;
        }
        
        // 简单解析JSON字符串
        // 注意：这是一个简化的实现，实际项目中应使用Jackson或Gson等库
        json = json.substring(1, json.length() - 1); // 去除首尾的{}
        
        if (json.isEmpty()) {
            return result;
        }
        
        String[] pairs = json.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();
                key = key.substring(1, key.length() - 1); // 去除引号
                
                try {
                    Long value = Long.parseLong(keyValue[1].trim());
                    result.put(key, value);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }
        
        return result;
    }
} 