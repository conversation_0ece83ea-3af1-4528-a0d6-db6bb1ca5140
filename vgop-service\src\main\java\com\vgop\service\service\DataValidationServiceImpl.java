package com.vgop.service.service;

import com.vgop.service.config.ValidationRulesProperties;
import com.vgop.service.dao.ValidationAlertsMapper;
import com.vgop.service.entity.ValidationAlert;
import com.vgop.service.validation.ValidationEngine;
import com.vgop.service.validation.ValidationResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataValidationServiceImpl implements DataValidationService {

    private final ValidationEngine validationEngine;
    private final ValidationRulesProperties validationRulesProperties;
    private final ValidationAlertsMapper validationAlertsMapper;

    @Override
    public String validateAndCleanFile(String originalFilePath, String interfaceId, String dataDate, String taskType) throws IOException {
        log.info("开始校验和清理文件: {}, 接口ID: {}", originalFilePath, interfaceId);
        File originalFile = new File(originalFilePath);
        if (!originalFile.exists()) {
            throw new IOException("原始文件不存在: " + originalFilePath);
        }

        String cleanedFilePath = originalFilePath.replace(".unl", ".cleaned.unl");
        Path tempPath = null;
        long lineNumber = 1;

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(originalFilePath), StandardCharsets.UTF_8)) {
            tempPath = Files.createTempFile("validation_", ".tmp");
            try (BufferedWriter writer = Files.newBufferedWriter(tempPath, StandardCharsets.UTF_8)) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 解析行数据
                    Map<String, String> rowData = parseLineToRowData(line, interfaceId);
                    
                    // 执行校验
                    List<ValidationResult> results = validationEngine.validateRow(
                            interfaceId, rowData, lineNumber, originalFile.getName(), dataDate);
                    
                    if (results.isEmpty()) {
                        // 没有校验错误，保留该行
                        writer.write(line);
                        writer.newLine();
                    } else {
                        // 有校验错误，记录告警并跳过该行
                        log.warn("文件 {} 第 {} 行校验失败: {} 个错误", originalFile.getName(), lineNumber, results.size());
                        for (ValidationResult result : results) {
                            saveValidationAlert(result, interfaceId, originalFile.getName(), lineNumber, line);
                        }
                    }
                    lineNumber++;
                }
            }
        }

        Files.move(tempPath, Paths.get(cleanedFilePath), StandardCopyOption.REPLACE_EXISTING);
        log.info("文件校验和清理完成, 清理后的文件: {}", cleanedFilePath);

        return cleanedFilePath;
    }

    /**
     * 解析行数据为字段映射
     * 
     * @param line 数据行
     * @param interfaceId 接口ID
     * @return 字段映射
     */
    private Map<String, String> parseLineToRowData(String line, String interfaceId) {
        Map<String, String> rowData = new HashMap<>();
        
        // 获取接口配置
        ValidationRulesProperties.InterfaceConfig interfaceConfig = 
                validationRulesProperties.getInterfaces().get(interfaceId.replace(".", "-"));
        
        if (interfaceConfig == null) {
            log.warn("未找到接口配置: {}", interfaceId);
            return rowData;
        }
        
        // 获取分隔符
        String delimiter = interfaceConfig.getDelimiter();
        if (delimiter == null) {
            // 使用默认的二进制分隔符
            delimiter = "\\|"; // 对应ASCII 128
        }
        
        // 分割数据行
        String[] values = line.split(delimiter);
        
        // 根据接口配置映射字段
        if (interfaceConfig.getFields() != null && !interfaceConfig.getFields().isEmpty()) {
            // 使用字段配置
            for (ValidationRulesProperties.FieldConfig fieldConfig : interfaceConfig.getFields()) {
                int index = fieldConfig.getFieldIndex();
                if (index >= 0 && index < values.length) {
                    rowData.put(fieldConfig.getFieldName(), values[index]);
                }
            }
        } else {
            // 使用默认字段名（适用于VGOP1-R2.10-24201接口）
            String[] defaultFieldNames = getDefaultFieldNames(interfaceId);
            for (int i = 0; i < Math.min(values.length, defaultFieldNames.length); i++) {
                rowData.put(defaultFieldNames[i], values[i]);
            }
        }
        
        return rowData;
    }
    
    /**
     * 获取接口的默认字段名
     * 
     * @param interfaceId 接口ID
     * @return 字段名数组
     */
    private String[] getDefaultFieldNames(String interfaceId) {
        switch (interfaceId) {
            case "VGOP1-R2.10-24201":
                return new String[]{
                    "line_number",    // 行号
                    "phonenumber",    // 电话号码
                    "phonestate",     // 电话状态
                    "phoneimsi",      // IMSI
                    "phoneimei",      // IMEI
                    "locationid",     // 位置ID
                    "provinceid",     // 省份ID
                    "openingtime",    // 开通时间
                    "Optime",         // 操作时间
                    "sex"             // 性别
                };
            // 其他接口的字段配置可以在这里添加
            default:
                log.warn("未知的接口ID: {}, 使用通用字段名", interfaceId);
                return new String[]{"field1", "field2", "field3", "field4", "field5", 
                                  "field6", "field7", "field8", "field9", "field10"};
        }
    }

    private void saveValidationAlert(ValidationResult result, String interfaceId, String fileName, long lineNumber, String errorData) {
        try {
            ValidationRulesProperties.InterfaceConfig interfaceConfig = validationRulesProperties.getInterfaces().get(interfaceId);
            String interfaceName = interfaceId;

            ValidationAlert alert = ValidationAlert.builder()
                    .alertTime(LocalDateTime.now())
                    .interfaceName(interfaceName)
                    .alertType("数据校验失败")
                    .alertLevel(ValidationAlert.AlertLevel.ERROR)
                    .alertMessage(result.getMessage())
                    .fileName(fileName)
                    .lineNumber(lineNumber)
                    .errorData(errorData)
                    .status(ValidationAlert.AlertStatus.NEW)
                    .build();
            validationAlertsMapper.insert(alert);
        } catch (Exception e) {
            log.error("保存校验告警信息失败", e);
        }
    }
}