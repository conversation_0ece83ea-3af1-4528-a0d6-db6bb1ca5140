#!/bin/sh
# MmeActionInsId MmeImagePath MmeDateId 
# 

while [ $# -gt 0 ] ; 
   do
     echo "$1"
     eval $1
     shift
   done
   
MmeDate="${MmeDateId:0:8}"   

BeforeDay=$(date +"%Y%m%d" -d"${MmeDate} -1day")

ImagePath=$(cd $MmeImagePath;pwd)

MarkId=$MmeActionInsId   

LogPath=$ImagePath/log
if [ ! -d $LogPath ]; then  
    mkdir $LogPath 
fi
LogName=$LogPath/$MarkId.log
:>$LogName

daydatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeDay}  >>$LogName

FTP_UserName="yikaduohao2vgop"
FTP_PassWord="Yi+2016-09kduoha"
FTP_IP="**************"
#"***************"
FTP_Port="6003"
PATH_Target="/data/yikaduohao/"

echo "FTP_UserName=$FTP_UserName" >>$LogName
echo "FTP_PassWord=$FTP_PassWord" >>$LogName
echo "FTP_IP=$FTP_IP" >>$LogName
echo "FTP_Port=$FTP_Port" >>$LogName
echo "PATH_Target=$PATH_Target" >>$LogName  


#新VGOP主机：
FTP_UserName1="yikaduohao2vgop"
FTP_PassWord1="Lg2021y-kD_Huc"
FTP_IP1="************"
FTP_Port1="22"
PATH_Target="/data/yikaduohao/"


lftp -p $FTP_Port -u "$FTP_UserName,$FTP_PassWord" sftp://$FTP_IP 1>>$LogName 2>&1  <<FTPEOF
mirror -I *.dat -I *.verf -R $daydatapath $PATH_Target

bye
FTPEOF

#新VGOP主机上传：

lftp -p $FTP_Port1 -u "$FTP_UserName1,$FTP_PassWord1" sftp://$FTP_IP1 1>>$LogName 2>&1  <<FTPEOF
mirror -I *.dat -I *.verf -R $daydatapath $PATH_Target



bye
FTPEOF