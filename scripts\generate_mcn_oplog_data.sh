#!/bin/bash
#
# 脚本功能: 为 mcn_oplog 表生成并导入测试数据。
#           此表用于多个接口, 如 24203 (opmanner=4) 和 24204 (opmanner=9)。
# 使用方法: ./generate_mcn_oplog_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_oplog_data.sh 20250612 500 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
DATA_FILE=$(mktemp "mcn_oplog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 mcn_oplog 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_INTL_PREFIX="00$(date +%N | cut -c 1-4)"

generate_phone_international() {
    printf "%s%05d" "${PHONE_INTL_PREFIX}" "$1"
}

generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

echo "正在生成数据..."
for i in $(seq 1 ${DATA_COUNT})
do
    phonenumber=$(generate_phone_international $i)
    optime=$(generate_datetime)
    
    # 随机生成 opmanner 和 optype 以覆盖不同接口的查询条件
    # 50% 的概率 opmanner=4 (用于24203), 50% opmanner=9 (用于24204)
    if [ $((RANDOM % 2)) -eq 0 ]; then
        opmanner=4
        optype_opts=("a" "b" "c" "d") # 24203接口不关心optype, 随机生成
        optype=${optype_opts[$RANDOM % ${#optype_opts[@]}]}
    else
        opmanner=9
        optype_opts=("s" "z") # 24204接口关心's'和'z'
        optype=${optype_opts[$RANDOM % ${#optype_opts[@]}]}
    fi
    
    # mcn_oplog 表可能还有其他列, 这里只生成SQL中用到的
    # 假设列顺序: phonenumber, optime, opmanner, optype
    echo "${phonenumber}${DELIMITER}${optype}${DELIMITER}${optime}${DELIMITER}${qmcnnumber}${DELIMITER}${hmcnnumber}${DELIMITER}${blacknumber}${DELIMITER}${whitenumber}${DELIMITER}${qphonenumber}${DELIMITER}${opmanner}${DELIMITER}${locationid}${DELIMITER}${provinceid}${DELIMITER}${mcnnature}${DELIMITER}${servid}${DELIMITER}${activityid}${DELIMITER}${adsource}${DELIMITER}${media}${DELIMITER}${processflag}${DELIMITER}${tailtype}" >> "${DATA_FILE}"
done
echo -e "\n数据生成完毕."
echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_oplog (phonenumber, optype, optime, qmcnnumber, hmcnnumber, blacknumber, whitenumber, qphonenumber, opmanner, locationid, provinceid, mcnnature, servid, activityid, adsource, media, processflag, tailtype);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 