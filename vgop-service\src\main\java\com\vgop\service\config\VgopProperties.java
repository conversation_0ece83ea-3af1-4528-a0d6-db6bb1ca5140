package com.vgop.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * VGOP应用配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "vgop")
public class VgopProperties {
    
    /**
     * 数据库配置
     */
    private Database database = new Database();
    
    /**
     * 文件处理配置
     */
    private FileProcessing fileProcessing = new FileProcessing();
    
    /**
     * 任务调度配置
     */
    private Scheduler scheduler = new Scheduler();
    
    /**
     * 获取数据导出根路径
     * 
     * @return 数据导出根路径
     */
    public String getExportPath() {
        return fileProcessing.getDataRootPath();
    }
    
    @Data
    public static class Database {
        /**
         * 默认列分隔符
         */
        private String defaultColumnSeparator = "|";
        
        /**
         * 数据库连接超时时间（秒）
         */
        private Integer connectionTimeout = 30;
        
        /**
         * 查询超时时间（秒）
         */
        private Integer queryTimeout = 300;
        
        /**
         * 锁等待时间（秒）
         */
        private Integer lockWaitTime = 10;
    }
    
    @Data
    public static class FileProcessing {
        /**
         * 每个文件的最大行数
         */
        private Integer maxLinesPerFile = 2000000;
        
        /**
         * 数据文件根目录
         */
        private String dataRootPath = "/VGOPdata/datafile";
        
        /**
         * 日统计子目录
         */
        private String daySubPath = "day";
        
        /**
         * 月统计子目录
         */
        private String monthSubPath = "month";
        
        /**
         * 临时文件后缀
         */
        private String tempFileSuffix = ".unl";
        
        /**
         * 数据文件后缀
         */
        private String dataFileSuffix = ".dat";
        
        /**
         * 校验文件后缀
         */
        private String verfFileSuffix = ".verf";
        
        /**
         * 文件编码
         */
        private String fileEncoding = "UTF-8";
        
        /**
         * 行分隔符替换字符 (ASCII 200)
         */
        private String lineDelimiterReplacement = "\200";
        
        /**
         * 行结束符
         */
        private String lineEnding = "\r";
    }
    
    @Data
    public static class Scheduler {
        /**
         * 线程池核心线程数
         */
        private Integer corePoolSize = 5;
        
        /**
         * 线程池最大线程数
         */
        private Integer maxPoolSize = 10;
        
        /**
         * 队列容量
         */
        private Integer queueCapacity = 100;
        
        /**
         * 线程空闲时间（秒）
         */
        private Integer keepAliveSeconds = 60;
        
        /**
         * 任务执行超时时间（分钟）
         */
        private Integer taskTimeoutMinutes = 60;
    }
} 