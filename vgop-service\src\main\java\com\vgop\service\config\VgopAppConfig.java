package com.vgop.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * VGOP应用配置
 * 映射application.yml中的app配置
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class VgopAppConfig {
    
    /**
     * 调度配置
     */
    private Schedules schedules;
    
    /**
     * 基础路径配置
     */
    private BasePath basePath;
    
    /**
     * SFTP配置
     */
    private SftpConfig sftp;
    
    /**
     * 文件处理配置
     */
    private FileProcessing fileProcessing;
    
    /**
     * 任务配置
     */
    private Tasks tasks;
    
    /**
     * 告警配置
     */
    private AlertConfig alert;
    
    /**
     * 备份配置
     */
    private BackupConfig backup;
    
    @Data
    public static class Schedules {
        private String dailyCron;
        private String monthlyCron;
    }
    
    @Data
    public static class BasePath {
        private String exportRoot;
        private String logRoot;
        private String backupRoot;
    }
    
    @Data
    public static class SftpConfig {
        private String host;
        private int port = 22;
        private String username;
        private String password;
        private String remoteBasePath;
        private int connectionTimeout = 30000;
        private int retryTimes = 3;
        private int maxPoolSize = 5;
    }
    
    @Data
    public static class FileProcessing {
        private int maxRowsPerFile = 2000000;
        private String defaultDelimiter = "|";
        private String outputDelimiter = "\\200";
        private String lineEnding = "\\r";
        private boolean addLineNumber = true;
        private String lineNumberDelimiter = "|";
    }
    
    @Data
    public static class Tasks {
        private List<TaskConfig> daily;
        private List<TaskConfig> monthly;
    }
    
    @Data
    public static class AlertConfig {
        private Storage storage;
        private UploadDeadline uploadDeadline;
        private Thresholds thresholds;
        
        @Data
        public static class Storage {
            private boolean enableDatabase = true;
            private boolean enableFile = false;
            private String alertFilePath;
        }
        
        @Data
        public static class UploadDeadline {
            private int daily = 8;
            private int monthly = 8;
        }
        
        @Data
        public static class Thresholds {
            private int maxAlertsPerBatch = 1000;
        }
    }
    
    @Data
    public static class BackupConfig {
        private int retentionDays = 7;
        private boolean enabled = true;
    }
    
    /**
     * 根据接口ID获取日任务配置
     */
    public TaskConfig getDailyTask(String interfaceId) {
        if (tasks == null || tasks.getDaily() == null) {
            return null;
        }
        return tasks.getDaily().stream()
                .filter(task -> task.getInterfaceId().equals(interfaceId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据接口ID获取月任务配置
     */
    public TaskConfig getMonthlyTask(String interfaceId) {
        if (tasks == null || tasks.getMonthly() == null) {
            return null;
        }
        return tasks.getMonthly().stream()
                .filter(task -> task.getInterfaceId().equals(interfaceId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取所有启用的日任务
     */
    public List<TaskConfig> getEnabledDailyTasks() {
        if (tasks == null || tasks.getDaily() == null) {
            return Collections.emptyList();
        }
        return tasks.getDaily().stream()
                .filter(TaskConfig::isEnabled)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有启用的月任务
     */
    public List<TaskConfig> getEnabledMonthlyTasks() {
        if (tasks == null || tasks.getMonthly() == null) {
            return Collections.emptyList();
        }
        return tasks.getMonthly().stream()
                .filter(TaskConfig::isEnabled)
                .collect(Collectors.toList());
    }
} 