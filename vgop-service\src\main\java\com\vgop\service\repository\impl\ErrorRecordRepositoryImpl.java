package com.vgop.service.repository.impl;

import com.vgop.service.repository.ErrorRecordRepository;
import com.vgop.service.validation.ErrorRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 错误记录仓库实现类
 */
@Repository
public class ErrorRecordRepositoryImpl implements ErrorRecordRepository {
    
    /**
     * JDBC模板
     */
    private final JdbcTemplate jdbcTemplate;
    
    /**
     * 错误记录行映射器
     */
    private final RowMapper<ErrorRecord> rowMapper = (rs, rowNum) -> ErrorRecord.builder()
            .id(rs.getLong("id"))
            .interfaceName(rs.getString("interface_name"))
            .fileName(rs.getString("file_name"))
            .dataDate(rs.getString("data_date"))
            .rowNumber(rs.getLong("row_number"))
            .fieldName(rs.getString("field_name"))
            .fieldValue(rs.getString("field_value"))
            .errorMessage(rs.getString("error_message"))
            .ruleId(rs.getString("rule_id"))
            .ruleName(rs.getString("rule_name"))
            .severity(rs.getString("severity"))
            .createTime(rs.getTimestamp("create_time").toLocalDateTime())
            .build();
    
    @Autowired
    public ErrorRecordRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    @Override
    public ErrorRecord save(ErrorRecord errorRecord) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(
                    "INSERT INTO vgop_error_records (interface_name, file_name, data_date, row_number, " +
                    "field_name, field_value, error_message, rule_id, rule_name, severity, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    Statement.RETURN_GENERATED_KEYS);
            
            ps.setString(1, errorRecord.getInterfaceName());
            ps.setString(2, errorRecord.getFileName());
            ps.setString(3, errorRecord.getDataDate());
            ps.setLong(4, errorRecord.getRowNumber());
            ps.setString(5, errorRecord.getFieldName());
            ps.setString(6, errorRecord.getFieldValue());
            ps.setString(7, errorRecord.getErrorMessage());
            ps.setString(8, errorRecord.getRuleId());
            ps.setString(9, errorRecord.getRuleName());
            ps.setString(10, errorRecord.getSeverity());
            ps.setTimestamp(11, Timestamp.valueOf(
                    errorRecord.getCreateTime() != null ? errorRecord.getCreateTime() : LocalDateTime.now()));
            
            return ps;
        }, keyHolder);
        
        Number key = keyHolder.getKey();
        if (key != null) {
            errorRecord.setId(key.longValue());
        }
        
        return errorRecord;
    }
    
    @Override
    public List<ErrorRecord> findByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate, int page, int size) {
        
        int offset = page * size;
        
        return jdbcTemplate.query(
                "SELECT * FROM vgop_error_records " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ? " +
                "ORDER BY row_number ASC, field_name ASC " +
                "LIMIT ? OFFSET ?",
                rowMapper,
                interfaceName, fileName, dataDate, size, offset);
    }
    
    @Override
    public long countByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate) {
        
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM vgop_error_records " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ?",
                Long.class,
                interfaceName, fileName, dataDate);
    }
    
    @Override
    public int deleteByInterfaceNameAndFileNameAndDataDate(
            String interfaceName, String fileName, String dataDate) {
        
        return jdbcTemplate.update(
                "DELETE FROM vgop_error_records " +
                "WHERE interface_name = ? AND file_name = ? AND data_date = ?",
                interfaceName, fileName, dataDate);
    }
} 