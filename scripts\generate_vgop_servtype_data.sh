#!/bin/bash
#
# 脚本功能: 为 vgop_servtype 维度表生成并导入数据。
#           这是一个维度表，通常只需要一次性填充。
# 使用方法: ./generate_vgop_servtype_data.sh <数据库名>
# 示例:     ./generate_vgop_servtype_data.sh bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 1 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <数据库名>"
    exit 1
fi

DB_NAME=$1
DELIMITER="|"
DATA_FILE=$(mktemp "vgop_servtype_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中填充 'vgop_servtype' 表。"
echo "临时数据文件: ${DATA_FILE}"

# --- 生成固定的维度数据 ---
# ServID | ServName
cat > "${DATA_FILE}" << EOL
100001|基础通话服务
100002|月度短信包
100003|虚拟副号月费
100004|实体副号月费
100005|国际漫游服务
200001|企业总机服务
200002|企业彩铃
EOL

echo "数据生成完毕."
cat "${DATA_FILE}"
echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
# 先删除旧数据，再插入新数据，确保幂等性
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO vgop_servtype (ServID, ServName);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！请检查 dbaccess 的输出和日志。"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 