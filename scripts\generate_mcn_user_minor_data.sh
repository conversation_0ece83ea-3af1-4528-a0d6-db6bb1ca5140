#!/bin/bash
#
# 脚本功能: 为 mcn_user_minor 表生成并导入测试数据。
# 使用方法: ./generate_mcn_user_minor_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_user_minor_data.sh 20250612 1000 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
DATA_FILE=$(mktemp "mcn_user_minor_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 mcn_user_minor 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_PREFIX="15$(date +%N | cut -c 1-5)"
PHONE_INTL_PREFIX="00$(date +%N | cut -c 1-4)"
IMSI_PREFIX="4601$(date +%N | cut -c 1-7)"

generate_phone() {
    printf "%s%04d" "${PHONE_PREFIX}" "$1"
}

generate_phone_international() {
    printf "%s%05d" "${PHONE_INTL_PREFIX}" "$1"
}

generate_imsi() {
    printf "%s%04d" "${IMSI_PREFIX}" "$1"
}

generate_7_digit_binary() {
    tr -dc '0-1' < /dev/urandom | head -c 7
}

generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

# --- 生成数据 ---
# 根据数据字典 'mcn_user_minor.shutdown' 字段的定义
SHUTDOWN_VALS=('0' '1' '2' '4' '5' '6' '7' '8' '9')
SHUTDOWN_COUNT=${#SHUTDOWN_VALS[@]}

# 根据数据字典 'mcn_user_minor.channel' 字段的定义
CHANNEL_VALS=('0' '1' '2' '3' '4' '5' '6' '7' '8' '9')

echo "正在生成数据..."
for i in $(seq 1 ${DATA_COUNT})
do
    mcnnumber=$(generate_phone $i)
    phonenumber=$(generate_phone_international $i)
    business_opts=("0" "1" "2" "3" "4")
    business=${business_opts[$RANDOM % ${#business_opts[@]}]}
    shutdown=$(shuf -i 0-99 -n 1)
    mcnimsi=$(generate_imsi $i)
    mcnlocationid=$(shuf -i 100-99999 -n 1)
    numstate=$(generate_7_digit_binary)
    mcnnature_opts=("0" "1")
    mcnnature=${mcnnature_opts[$RANDOM % ${#mcnnature_opts[@]}]}
    mcnnum_opts=("1" "2" "3")
    mcnnum=${mcnnum_opts[$RANDOM % ${#mcnnum_opts[@]}]}
    channel=$(shuf -i 1-99 -n 1)
    anothername=""
    openingtime=$(generate_datetime)
    optime=$(generate_datetime)
    mcimsitime=$(generate_datetime)
    atitime=$(generate_datetime)
    mcnstate="0"
    mcntype="0"
    begintime=$(generate_datetime)
    endtime=$(generate_datetime)
    servid=$(shuf -i 100000-999999 -n 1)

    echo "${mcnnumber}${DELIMITER}${phonenumber}${DELIMITER}${business}${DELIMITER}${shutdown}${DELIMITER}${mcnimsi}${DELIMITER}${mcnlocationid}${DELIMITER}${numstate}${DELIMITER}${mcnnature}${DELIMITER}${mcnnum}${DELIMITER}${channel}${DELIMITER}${anothername}${DELIMITER}${openingtime}${DELIMITER}${optime}${DELIMITER}${mcimsitime}${DELIMITER}${atitime}${DELIMITER}${mcnstate}${DELIMITER}${mcntype}${DELIMITER}${begintime}${DELIMITER}${endtime}${DELIMITER}${servid}" >> "${DATA_FILE}"
done
echo -e "\n数据生成完毕."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_user_minor (mcnnumber, phonenumber, business, shutdown, mcnimsi, mcnlocationid, numstate, mcnnature, mcnnum, channel, anothername, openingtime, optime, mcimsitime, atitime, mcnstate, mcntype, begintime, endtime, servid);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 