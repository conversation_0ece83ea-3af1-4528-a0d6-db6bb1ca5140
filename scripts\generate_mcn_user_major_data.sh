#!/bin/bash
#
# 脚本功能: 为 mcn_user_major 表生成并导入测试数据.
# 使用方法: ./generate_mcn_user_major_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_user_major_data.sh 20250612 1000 bms
#

# --- 配置区 ---
# 请根据您的环境，将此变量修改为 dbaccess 命令的绝对路径。
# 如果 'dbaccess' 已经在您的 PATH 中，则无需修改。
# 示例: DBACCESS_CMD="/opt/informix/bin/dbaccess"
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
# 使用 mktemp 创建一个安全的临时文件
DATA_FILE=$(mktemp "mcn_user_major_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 mcn_user_major 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_PREFIX="13$(date +%N | cut -c 1-5)"

generate_phone_11_digit() {
    printf "%s%04d" "${PHONE_PREFIX}" "$1"
}

generate_imsi() {
    tr -dc '0-9' < /dev/urandom | head -c 15
}

generate_imei() {
    tr -dc '0-9' < /dev/urandom | head -c 15
}

generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

# --- 生成数据 ---
# 可用的 provinceid 列表 (来自 bossprovince)
PROVINCE_IDS=(100 200 210 220 230 240 250 270 280 290 310 350 370 390 430 450 470 490 530 550 590 730 750 770 790 810 850 870 890 910 930 950 970)
PROVINCE_COUNT=${#PROVINCE_IDS[@]}

> "${DATA_FILE}" # 清空文件

for i in $(seq 1 ${DATA_COUNT}); do
    phonenumber=$(generate_phone_11_digit $i)
    phonestate=$(($RANDOM % 2)) # 0 or 1
    phoneimsi=$(generate_imsi)
    phoneimei=$(generate_imei)
    locationid=$(printf "%05d" $(($RANDOM % 99999)))
    provinceid=${PROVINCE_IDS[$(($i % $PROVINCE_COUNT))]}
    applytimes=$(($RANDOM % 10)) # 申请次数，0-9
    openingtime=$(generate_datetime)
    optime=$(generate_datetime)
    sex=$(($RANDOM % 2)) # 0 or 1
    voicenumber1=$(($RANDOM % 100))
    voicenumber2=$(($RANDOM % 100))
    portinflag_val=$(($RANDOM % 3))
    # 确保 portinflag 总是有值：0, 1, 或 2 (对应非携入、联通携入、电信携入)
    portinflag=$portinflag_val

    echo "${phonenumber}${DELIMITER}${phonestate}${DELIMITER}${phoneimsi}${DELIMITER}${phoneimei}${DELIMITER}${locationid}${DELIMITER}${provinceid}${DELIMITER}${applytimes}${DELIMITER}${openingtime}${DELIMITER}${optime}${DELIMITER}${sex}${DELIMITER}${voicenumber1}${DELIMITER}${voicenumber2}${DELIMITER}${portinflag}" >> "${DATA_FILE}"
done

echo "数据生成完毕."
echo "准备将数据导入数据库..."

# 显示生成的数据文件内容以便调试
echo "生成的数据文件前3行内容："
head -3 "${DATA_FILE}"
echo ""
echo "第1行字段数量："
head -1 "${DATA_FILE}" | awk -F'|' '{print NF}'
echo ""

# --- 准备 LOAD 命令 ---
# 注意：DELETE FROM ... 是危险操作，此处仅为方便测试环境重复执行
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_user_major (phonenumber, phonestate, phoneimsi, phoneimei, locationid, provinceid, applytimes, openingtime, optime, sex, voicenumber1, voicenumber2, portinflag);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 执行导入 ---
echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -

# --- 清理 ---
rm "${DATA_FILE}"

echo "mcn_user_major 表数据导入完成。" 