#!/bin/bash
#
# 脚本功能: 为 mcn_sec_major 和 mcn_sec_major2 表生成并导入测试数据。
# 使用方法: ./generate_mcn_sec_major_data.sh <表名> <日期YYYYMMDD> <数量> <数据库名>
# 示例:
#   ./generate_mcn_sec_major_data.sh mcn_sec_major 20250612 500 bms
#   ./generate_mcn_sec_major_data.sh mcn_sec_major2 20250612 500 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 4 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <表名: mcn_sec_major|mcn_sec_major2> <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TABLE_NAME=$1
TARGET_DATE=$2
DATA_COUNT=$3
DB_NAME=$4
DELIMITER="|"

if [ "$TABLE_NAME" != "mcn_sec_major" ] && [ "$TABLE_NAME" != "mcn_sec_major2" ]; then
    echo "错误: 无效的表名. 请使用 'mcn_sec_major' 或 'mcn_sec_major2'."
    exit 1
fi

DATA_FILE=$(mktemp "${TABLE_NAME}_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")
echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 ${TABLE_NAME} 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_PREFIX="13$(date +%N | cut -c 1-5)"
MCN_PREFIX="14$(date +%N | cut -c 1-5)"
IMSI_PREFIX="4603$(date +%N | cut -c 1-7)"

generate_phone() { printf "%s%04d" "${PHONE_PREFIX}" "$1"; }
generate_mcn() { printf "%s%04d" "${MCN_PREFIX}" "$1"; }
generate_imsi() { printf "%s%04d" "${IMSI_PREFIX}" "$1"; }
generate_7_digit_binary() { tr -dc '0-1' < /dev/urandom | head -c 7; }
generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

echo "正在生成数据..."
for i in $(seq 1 ${DATA_COUNT})
do
    phonenumber=$(generate_phone $i)
    mcnnumber=$(generate_mcn $i)
    mcnImsi=$(generate_imsi $i)
    # 脚本会查询 businessState='X' 的数据(月度)，以及普通状态数据(每日)
    state_opts=("0" "1" "2" "3" "4" "X")
    businessState=${state_opts[$RANDOM % ${#state_opts[@]}]}
    numstate=$(generate_7_digit_binary)
    locationid=$(shuf -i 100-99999 -n 1)
    # 从预定义的省份ID列表中随机选择一个
    province_ids=(100 200 210 220 230 240 250 270 280 290 310 350 370 390 430 450)
    bossProvinceid=${province_ids[$RANDOM % ${#province_ids[@]}]}
    openingtime=$(generate_datetime)

    echo "${phonenumber}${DELIMITER}${mcnnumber}${DELIMITER}${mcnImsi}${DELIMITER}${businessState}${DELIMITER}${numstate}${DELIMITER}${locationid}${DELIMITER}${bossProvinceid}${DELIMITER}${openingtime}" >> "${DATA_FILE}"
done
echo -e "\n数据生成完毕."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO ${TABLE_NAME} (phonenumber, mcnnumber, mcnImsi, businessState, Numstate, Locationid, BossProvinceid, openingtime, flag);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 