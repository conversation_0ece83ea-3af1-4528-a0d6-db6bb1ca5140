#!/bin/bash
#
# 脚本功能: 为 Mcn_contralog 表生成并导入测试数据。
# 使用方法: ./generate_mcn_contralog_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_contralog_data.sh 20250612 1000 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
DATA_FILE=$(mktemp "mcn_contralog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 Mcn_contralog 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_PREFIX="18$(date +%N | cut -c 1-5)"

generate_phone_11_digit() {
    printf "%s%04d" "${PHONE_PREFIX}" "$1"
}

generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

# --- 生成数据 ---
REASON_VALS=('0' '1' '2' '3' '4' '5' '6' '7' '8' '9' '10' '11' '12' '13' '14' '15' '16' '17')
REASON_COUNT=${#REASON_VALS[@]}

SHUTDOWN_VALS=('0' '1' '2' '4' '5' '6' '8')
SHUTDOWN_COUNT=${#SHUTDOWN_VALS[@]}

echo "正在生成数据..."
for i in $(seq 1 ${DATA_COUNT})
do
    serviceKey=$(($RANDOM * 32767))
    callType=$(($RANDOM % 2))
    
    # 脚本约束: calling/called 都是11位且以1开头
    callingPartyNumber=$(generate_phone_11_digit $i)
    # 为避免号码重复，给 calledPartyNumber 一个不同的序列
    calledPartyNumber=$(generate_phone_11_digit $(($i + ${DATA_COUNT}))))
    mcnnumber=$(generate_phone_11_digit $(($i + 2 * ${DATA_COUNT}))))

    callBeginTime=$(generate_datetime)
    
    # 保证结束时间晚于开始时间
    end_ts=$(date -d "${callBeginTime:0:8} ${callBeginTime:8:2}:${callBeginTime:10:2}:${callBeginTime:12:2}" +%s)
    duration=$(shuf -i 1-3600 -n 1)
    new_end_ts=$((end_ts + duration))
    callEndTime=$(date -d "@$new_end_ts" +%Y%m%d%H%M%S)

    chargepartyindicat=$(($RANDOM % 2 + 1))
    reason=${REASON_VALS[$(($RANDOM % $REASON_COUNT))]}
    business=$(($RANDOM % 4))
    shutdown=${SHUTDOWN_VALS[$(($RANDOM % $SHUTDOWN_COUNT))]}
    displayNumber=$(generate_phone_11_digit $(($i + 1000))) # use different number
    hostName="host_$(($RANDOM % 4 + 1))"
    egroupflag=$(($RANDOM % 2))

    echo "${serviceKey}${DELIMITER}${callType}${DELIMITER}${chargepartyindicat}${DELIMITER}${callingPartyNumber}${DELIMITER}${calledPartyNumber}${DELIMITER}${mcnnumber}${DELIMITER}${displayNumber}${DELIMITER}${hostName}${DELIMITER}${egroupflag}" >> "${DATA_FILE}"
done
echo -e "\n数据生成完毕."

echo "准备将数据导入数据库..."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO Mcn_contralog (serviceKey, callType, chargeType, roamFlag, callingPartyNumber, calledPartyNumber, mcnnumber, roamAreaNumber, MSCAddress, callReferenceNum, MSInfoVLRNumber, MSInfoLAI, MSInfoCellId, MSInfoIMSI, CallBeginTime, CallEndTime, CallDuration, chargepartyindicat, reason, business, shutdown, displayNumber, hostName, egroupflag);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 