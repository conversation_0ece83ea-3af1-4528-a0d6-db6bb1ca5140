package com.vgop.service.sftp;

import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.exception.VgopException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * SFTP服务
 * 提供文件上传、目录管理、批量传输等功能
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class SftpService {
    
    private final VgopAppConfig appConfig;
    
    @Autowired
    public SftpService(VgopAppConfig appConfig) {
        this.appConfig = appConfig;
    }
    
    /**
     * 上传单个文件
     * 
     * @param localFilePath 本地文件路径
     * @param remoteFilePath 远程文件路径
     * @return 是否成功
     */
    public void uploadFile(String localFilePath, String remoteFilePath) throws VgopException {
        int maxRetries = appConfig.getSftp().getRetryTimes();
        int attempt = 0;
        Exception lastException = null;

        while (attempt <= maxRetries) {
            try {
                SftpUtil.upload(appConfig.getSftp(), localFilePath, remoteFilePath);
                return; // Success, exit the method
            } catch (VgopException e) {
                lastException = e;
                attempt++;
                log.error("文件上传失败 (尝试 {}/{})", attempt, maxRetries + 1, e);
                if (attempt <= maxRetries) {
                    try {
                        Thread.sleep(1000 * attempt); // Wait before retrying
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new VgopException("SFTP上传在重试等待中被中断", ie);
                    }
                }
            }
        }
        throw new VgopException("文件上传最终失败，已达最大重试次数: " + localFilePath, lastException);
    }
    
    /**
     * 批量上传文件
     * 
     * @param localFiles 本地文件列表
     * @param remoteDir 远程目录
     * @return 成功上传的文件列表
     */
    public List<String> uploadFiles(List<String> localFilePaths, String remoteDir) {
        List<String> failedFiles = new ArrayList<>();
        log.info("开始批量上传 {} 个文件到: {}", localFilePaths.size(), remoteDir);

        for (String localFilePath : localFilePaths) {
            try {
                Path localPath = Paths.get(localFilePath);
                String remoteFilePath = remoteDir + "/" + localPath.getFileName().toString();
                uploadFile(localFilePath, remoteFilePath);
            } catch (Exception e) {
                log.error("文件上传最终失败: {} -> {}", localFilePath, remoteDir, e);
                failedFiles.add(localFilePath);
            }
        }

        log.info("批量上传完成 - 成功: {}, 失败: {}", localFilePaths.size() - failedFiles.size(), failedFiles.size());
        if (!failedFiles.isEmpty()) {
            log.error("上传失败的文件: {}", failedFiles);
        }
        return failedFiles;
    }
    
    /**
     * 上传目录下的所有文件
     * 
     * @param localDir 本地目录
     * @param remoteDir 远程目录
     * @param filePattern 文件匹配模式（如 "*.dat", "*.verf"）
     * @return 成功上传的文件数量
     */
    public int uploadDirectory(String localDirectoryPath, String remoteDirectoryPath) throws VgopException {
        log.info("准备上传目录: {} -> {}", localDirectoryPath, remoteDirectoryPath);
        File localDir = new File(localDirectoryPath);
        if (!localDir.exists() || !localDir.isDirectory()) {
            throw new VgopException("本地目录不存在或不是一个目录: " + localDirectoryPath);
        }

        File[] files = localDir.listFiles();
        if (files == null || files.length == 0) {
            log.warn("本地目录为空，无需上传: {}", localDirectoryPath);
            return 0;
        }
        
        int successCount = 0;
        for (File file : files) {
            if (file.isFile()) {
                String remoteFilePath = remoteDirectoryPath + "/" + file.getName();
                try {
                    uploadFile(file.getAbsolutePath(), remoteFilePath);
                    successCount++;
                } catch (VgopException e) {
                    log.error("上传文件失败，将继续上传其他文件: {} -> {}", file.getAbsolutePath(), remoteFilePath, e);
                    // Optionally, you could collect failed files here
                }
            }
        }
        log.info("目录上传完成: {} -> {}, 成功上传 {} 个文件", localDirectoryPath, remoteDirectoryPath, successCount);
        return successCount;
    }
    
    /**
     * 上传VGOP数据文件到DMZ
     * 
     * @param dataDate 数据日期
     * @return 上传结果
     */
    public UploadResult uploadVgopDataFiles(String dataDate) {
        // 使用统一的路径配置
        String localPath = appConfig.getBasePath().getExportRoot();
        if (!localPath.endsWith("/")) {
            localPath += "/";
        }
        localPath += dataDate;
        
        String remotePath = appConfig.getSftp().getRemoteBasePath();
        if (!remotePath.endsWith("/")) {
            remotePath += "/";
        }
        remotePath += dataDate;
        
        log.info("=== 开始上传VGOP数据文件 - 日期: {} ===", dataDate);
        log.info("本地路径: {}", localPath);
        log.info("远程路径: {}", remotePath);
        
        UploadResult result = new UploadResult();
        result.setDataDate(dataDate);
        result.setStartTime(System.currentTimeMillis());
        
        // 检查本地目录是否存在
        File localDir = new File(localPath);
        if (!localDir.exists()) {
            log.error("本地数据目录不存在: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            return result;
        }
        
        if (!localDir.isDirectory()) {
            log.error("本地路径不是目录: {}", localPath);
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            return result;
        }
        
        // 上传.dat文件
        int datCount = uploadDirectory(localPath, remotePath);
        result.setDatFileCount(datCount);
        
        // 上传.verf文件
        int verfCount = uploadDirectory(localPath, remotePath);
        result.setVerfFileCount(verfCount);
        
        result.setEndTime(System.currentTimeMillis());
        result.setSuccess(datCount > 0 && verfCount > 0);
        
        log.info("=== VGOP数据文件上传完成 - .dat文件: {}, .verf文件: {}, 耗时: {}ms ===",
                datCount, verfCount, result.getDuration());
        
        return result;
    }
    
    /**
     * 上传结果
     */
    public static class UploadResult {
        private String dataDate;
        private boolean success;
        private int datFileCount;
        private int verfFileCount;
        private long startTime;
        private long endTime;
        
        public String getDataDate() { return dataDate; }
        public void setDataDate(String dataDate) { this.dataDate = dataDate; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public int getDatFileCount() { return datFileCount; }
        public void setDatFileCount(int datFileCount) { this.datFileCount = datFileCount; }
        
        public int getVerfFileCount() { return verfFileCount; }
        public void setVerfFileCount(int verfFileCount) { this.verfFileCount = verfFileCount; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return endTime - startTime; }
    }
} 