package com.vgop.service.controller;

import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig;
import com.vgop.service.service.FileTransferService;
import com.vgop.service.service.VgopTaskScheduler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.concurrent.CompletableFuture;

/**
 * VGOP任务管理REST API控制器
 */
@RestController
@RequestMapping("/api/v1/tasks")
@Api(tags = "VGOP任务执行")
@Slf4j
@RequiredArgsConstructor
public class VgopTaskController {
    
    private final VgopTaskScheduler taskScheduler;
    private final FileTransferService fileTransferService;

    @PostMapping("/execute")
    @ApiOperation(value = "执行单个统计任务", notes = "根据接口ID执行单个VGOP数据导出分析任务")
    public ResponseEntity<TaskExecutionResponse> executeTask(
            @ApiParam(value = "任务执行请求", required = true)
            @Valid @RequestBody TaskExecutionRequest request) {

        log.info("接收到任务执行请求: {}, 接口ID: {}", request.getActionInstanceId(), request.getInterfaceId());
        
        try {
            CompletableFuture<TaskExecutionResponse> future = taskScheduler.executeTask(request);

            if (request.getAsync() != null && request.getAsync()) {
                TaskExecutionResponse response = TaskExecutionResponse.builder()
                    .taskId(request.getActionInstanceId())
                    .status(TaskConfig.TaskStatus.PENDING)
                    .success(true)
                    .message("任务已提交，正在异步执行: " + request.getInterfaceId())
                    .build();
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.ok(future.get());
            }
            
        } catch (Exception e) {
            log.error("任务执行失败: {}", request.getInterfaceId(), e);
            TaskExecutionResponse errorResponse = TaskExecutionResponse.builder()
                .taskId(request.getActionInstanceId())
                .status(TaskConfig.TaskStatus.FAILED)
                .success(false)
                .errorMessage(e.getMessage())
                .message("任务执行失败: " + request.getInterfaceId())
                .build();
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/transfer/daily")
    @ApiOperation(value = "执行日度文件传输任务", notes = "手动触发日度数据文件的导出、校验和SFTP上传")
    public ResponseEntity<FileTransferService.TransferResult> executeDailyTransfer(
            @ApiParam(value = "数据日期 (格式: yyyyMMdd)", required = true) @RequestParam String dataDate) {
        log.info("接收到手动日度文件传输请求，数据日期: {}", dataDate);
        FileTransferService.TransferResult result = fileTransferService.processDailyTransfer(dataDate);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/transfer/monthly")
    @ApiOperation(value = "执行月度文件传输任务", notes = "手动触发月度数据文件的导出、校验和SFTP上传")
    public ResponseEntity<FileTransferService.TransferResult> executeMonthlyTransfer(
            @ApiParam(value = "数据日期 (格式: yyyyMMdd)", required = true) @RequestParam String dataDate) {
        log.info("接收到手动月度文件传输请求，数据日期: {}", dataDate);
        FileTransferService.TransferResult result = fileTransferService.processMonthlyTransfer(dataDate);
        return ResponseEntity.ok(result);
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/{taskId}/status")
    @ApiOperation(value = "查询任务状态", notes = "根据任务ID查询任务执行状态")
    public ResponseEntity<TaskExecutionResponse> getTaskStatus(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.info("查询任务状态: {}", taskId);
        
        // TODO: 实现任务状态查询逻辑
        // 这里需要维护一个任务状态的存储，可以是内存中的Map或数据库
        
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(taskId)
            .status(TaskConfig.TaskStatus.SUCCESS)
            .success(true)
            .message("任务状态查询功能待实现")
            .build();
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    @ApiOperation(value = "系统健康检查", notes = "检查VGOP服务健康状态")
    public ResponseEntity<Object> healthCheck() {
        return ResponseEntity.ok().body(new Object() {
            public final String status = "UP";
            public final String service = "VGOP Service";
            public final long timestamp = System.currentTimeMillis();
        });
    }
} 