#!/bin/bash
#
# 脚本功能: 为 mcn_apploginlog 表生成并导入测试数据。
# 使用方法: ./generate_mcn_apploginlog_data.sh <日期YYYYMMDD> <数量> <数据库名>
# 示例:     ./generate_mcn_apploginlog_data.sh 20250612 500 bms
#

# --- 配置区 ---
DBACCESS_CMD="dbaccess"

# --- 主脚本逻辑 ---

if [ "$#" -ne 3 ]; then
    echo "错误: 参数不足。"
    echo "用法: $0 <日期YYYYMMDD> <数量> <数据库名>"
    exit 1
fi

TARGET_DATE=$1
DATA_COUNT=$2
DB_NAME=$3
DELIMITER="|"
DATA_FILE=$(mktemp "mcn_apploginlog_data_$(date +%Y%m%d%H%M%S)_XXXXXX.unl")

echo "将在数据库 '${DB_NAME}' 中为日期 '${TARGET_DATE}' 的 mcn_apploginlog 表生成 '${DATA_COUNT}' 条数据。"
echo "临时数据文件: ${DATA_FILE}"

# --- Helper Functions ---
PHONE_INTL_PREFIX="00$(date +%N | cut -c 1-4)"

generate_phone_international() {
    printf "%s%05d" "${PHONE_INTL_PREFIX}" "$1"
}

generate_datetime() {
    HOUR=$(printf "%02d" $(($RANDOM % 24)))
    MINUTE=$(printf "%02d" $(($RANDOM % 60)))
    SECOND=$(printf "%02d" $(($RANDOM % 60)))
    echo "${TARGET_DATE}${HOUR}${MINUTE}${SECOND}"
}

generate_version() {
    echo "$(shuf -i 1-5 -n 1).$(shuf -i 0-9 -n 1).$(shuf -i 0-20 -n 1)"
}

echo "正在生成数据..."
for i in $(seq 1 ${DATA_COUNT})
do
    phonenumber=$(generate_phone_international $i)
    logintime=$(generate_datetime)
    version=$(generate_version)

    echo "${phonenumber}${DELIMITER}${logintime}${DELIMITER}${version}" >> "${DATA_FILE}"
done
echo -e "\n数据生成完毕."

# --- 准备 LOAD 命令 ---
SQL="
set lock mode to wait 10;
LOAD FROM '${DATA_FILE}' DELIMITER '${DELIMITER}' INSERT INTO mcn_apploginlog (phonenumber, logintime, optype, appversion, apptype, loginstatus, hostName, egroupflag);
"

echo "将要执行以下SQL:"
echo -e "${SQL}"

# --- 使用 dbaccess 执行 LOAD 命令 ---
if echo "${SQL}" | ${DBACCESS_CMD} "${DB_NAME}" -; then
  echo "数据成功导入!"
  rm "${DATA_FILE}"
else
  echo "数据导入失败！"
  echo "临时数据文件 '${DATA_FILE}' 已保留。"
  exit 1
fi

echo "脚本执行完毕。" 