package com.vgop.service.dao;

import com.vgop.service.entity.ValidationAlert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 校验告警数据访问接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface ValidationAlertsMapper {

    /**
     * 根据告警ID查询告警信息
     * 
     * @param alertId 告警ID
     * @return 告警信息
     */
    ValidationAlert findById(@Param("alertId") Long alertId);

    /**
     * 根据接口名称和时间范围查询告警信息
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 告警信息列表
     */
    List<ValidationAlert> findByInterfaceAndDate(@Param("interfaceName") String interfaceName,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 根据状态查询告警信息
     * 
     * @param status 告警状态
     * @return 告警信息列表
     */
    List<ValidationAlert> findByStatus(@Param("status") ValidationAlert.AlertStatus status);

    /**
     * 插入告警信息
     * 
     * @param alert 告警信息
     * @return 插入记录数
     */
    int insert(ValidationAlert alert);

    /**
     * 更新告警状态
     * 
     * @param alertId 告警ID
     * @param status 新状态
     * @param handledBy 处理人
     * @param handledTime 处理时间
     * @return 更新记录数
     */
    int updateStatus(@Param("alertId") Long alertId,
                    @Param("status") ValidationAlert.AlertStatus status,
                    @Param("handledBy") String handledBy,
                    @Param("handledTime") LocalDateTime handledTime);

    /**
     * 根据告警ID删除告警信息
     * 
     * @param alertId 告警ID
     * @return 删除记录数
     */
    int deleteByAlertId(@Param("alertId") Long alertId);
} 