---
description: 
globs: 
alwaysApply: true
---
# VGOP数据校验系统 - 项目开发规则

## 项目概述
VGOP数据校验系统是一个现代化的Spring Boot微服务应用，用于替代Shell脚本架构，实现数据的自动化导出、校验和传输。

系统采用三阶段处理架构：**数据导出** → **数据校验** → **文件传输**

## 技术栈
- **框架**: Spring Boot 2.5.5 + Java 8
- **数据库**: Informix JDBC 4.50.7 (生产) / GBase JDBC (测试) + MyBatis 2.2.0
- **连接池**: Druid 1.2.8
- **文件传输**: SSHJ 0.40.0 (SFTP)
- **文件处理**: Apache POI 4.1.1 (Excel)
- **定时任务**: Spring Scheduler + Quartz
- **监控**: Spring Boot Actuator
- **日志**: SLF4J + Logback
- **构建**: Maven 3.x

## 项目结构

### 主要模块位置
- **主应用**: [VgopServiceApplication.java](mdc:vgop-service/src/main/java/com/vgop/service/VgopServiceApplication.java)
- **配置管理**: [vgop-service/src/main/java/com/vgop/service/config/](mdc:vgop-service/src/main/java/com/vgop/service/config)
- **业务服务**: [vgop-service/src/main/java/com/vgop/service/service/](mdc:vgop-service/src/main/java/com/vgop/service/service)
- **REST控制器**: [vgop-service/src/main/java/com/vgop/service/controller/](mdc:vgop-service/src/main/java/com/vgop/service/controller)
- **数据访问**: [vgop-service/src/main/java/com/vgop/service/dao/](mdc:vgop-service/src/main/java/com/vgop/service/dao)

### 核心配置文件
- **主配置**: [application.yml](mdc:vgop-service/src/main/resources/application.yml)
- **开发环境**: [application-dev.yml](mdc:vgop-service/src/main/resources/application-dev.yml)
- **测试环境**: [application-test.yml](mdc:vgop-service/src/main/resources/application-test.yml)
- **Maven配置**: [pom.xml](mdc:vgop-service/pom.xml)

## 核心模块说明

### 1. 数据导出模块
- **服务类**: [DataExportService.java](mdc:vgop-service/src/main/java/com/vgop/service/service/DataExportService.java)
- **功能**: 替代12个Shell脚本，执行UNLOAD TO命令导出数据
- **特性**: 支持11个日接口和4个月接口，版本控制，200万行文件分割

### 2. 数据校验模块  
- **服务类**: [DataValidationService.java](mdc:vgop-service/src/main/java/com/vgop/service/service/DataValidationService.java)
- **校验引擎**: [ValidationEngine.java](mdc:vgop-service/src/main/java/com/vgop/service/validation/ValidationEngine.java)
- **功能**: 多维度数据质量检查和校验报告生成

### 3. 文件传输模块
- **服务类**: [FileTransferService.java](mdc:vgop-service/src/main/java/com/vgop/service/service/FileTransferService.java)
- **SFTP工具**: [SftpService.java](mdc:vgop-service/src/main/java/com/vgop/service/sftp/SftpService.java)
- **功能**: SFTP文件上传和传输管理

### 4. 调度管理模块
- **调度器**: [VgopTaskScheduler.java](mdc:vgop-service/src/main/java/com/vgop/service/scheduler/VgopTaskScheduler.java)
- **功能**: 双周期调度（日/月）、任务锁机制、执行状态记录

## 开发规范

### 代码规范
1. 遵循阿里巴巴Java开发手册
2. 使用SLF4J进行日志记录，区分不同级别
3. 统一异常处理机制
4. 使用@ConfigurationProperties进行配置绑定

### 数据库配置
- **开发/测试环境**: 使用GBase数据库
- **生产环境**: 使用Informix数据库
- **连接配置**: 在对应的application-{env}.yml中配置

### 测试规范
- **重要**: 用户明确表示不需要编写单元测试，因为本地没办法执行
- 不要创建任何测试类、测试方法或测试相关的代码

### 文件处理规范
- 大文件处理使用流式处理避免内存溢出
- 200万行以上文件自动分割
- Excel报告生成使用Apache POI

## 环境配置

### 多环境支持
- **dev**: 开发环境，使用GBase数据库
- **test**: 测试环境，使用GBase数据库  
- **prod**: 生产环境，使用Informix数据库

### 关键配置项
- **数据库连接**: 双数据库支持（Informix/GBase）
- **SFTP配置**: 重试次数和超时时间
- **文件处理**: 分片大小和格式参数
- **调度配置**: 执行时间和线程池参数

## Shell脚本说明
项目包含原有的Shell脚本作为参考：
- **日数据脚本**: [sh/bms_VGOP_daystat/](mdc:sh/bms_VGOP_daystat)
- **月数据脚本**: [sh/bms_VGOP_monthstat/](mdc:sh/bms_VGOP_monthstat)
- **文档分析**: [document/sql_analysis/](mdc:document/sql_analysis)

## 开发注意事项

### 优先级指导
1. **高优先级**: 完善数据校验功能、增强告警系统、完善监控功能
2. **中优先级**: 集成测试、性能优化、运维功能
3. **低优先级**: 高级功能、外部集成、容量规划

### 部署相关
- 不需要进行打包或部署，用户会在自己的IDE中验证和编译
- 确保代码可以在Spring Boot环境中正常运行
- 关注配置文件的正确性和数据库连接

### 异常处理
- 使用统一的异常处理机制
- 自定义异常类位于[exception包](mdc:vgop-service/src/main/java/com/vgop/service/exception)
- 全局异常处理器: [GlobalExceptionHandler.java](mdc:vgop-service/src/main/java/com/vgop/service/exception/GlobalExceptionHandler.java)

## 重要文档参考
- **项目综合介绍**: [VGOP项目综合介绍文档.md](mdc:VGOP项目综合介绍文档.md)
- **实施清单**: [vgop-implementation-checklist.md](mdc:documents/vgop-implementation-checklist.md)
- **部署说明**: [部署脚本使用说明.md](mdc:部署脚本使用说明.md)

