#!/bin/bash

# VGOP本地代码部署脚本
# 适用于已有代码目录的部署场景

# set -e  # 移除严格错误退出，改为手动处理错误

# ================================
# 配置参数
# ================================

# 现有代码目录
SOURCE_DIR="/home/<USER>/workspaces/vgop-vli"
PROJECT_NAME="vgop-service"
JAR_NAME="vgop-service-1.0.0-SNAPSHOT.jar"

# 部署目录
DEPLOY_DIR="/home/<USER>/workspaces/vgop-vli"
SERVICE_DIR="$DEPLOY_DIR/vgop-service"

# Git配置
GIT_REPO_URL="ssh://gitlab.e-byte.cn:19222/hdh/website_group/vgop-vli"
GIT_BRANCH="master"

# 环境配置
JAVA_HOME="/home/<USER>/workspaces/jdk"  # 请根据实际情况修改
PROFILE="dev"  # 运行环境配置文件

# 服务配置
SERVICE_PORT="8080"
JVM_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:logs/gc.log"
LOG_FILE="logs/vgop-service.log"

# PID文件
PID_FILE="$SERVICE_DIR/$PROJECT_NAME.pid"

# ================================
# 颜色输出函数
# ================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

fatal_error() {
    echo -e "${RED}[FATAL ERROR]${NC} $1"
    exit 1
}

# ================================
# 环境检查函数
# ================================
check_environment() {
    info "检查运行环境..."
    
    # 检查源代码目录
    if [ ! -d "$SOURCE_DIR" ]; then
        fatal_error "源代码目录不存在: $SOURCE_DIR"
    fi
    
    # 检查vgop-service子目录
    if [ ! -d "$SOURCE_DIR/vgop-service" ]; then
        fatal_error "vgop-service目录不存在: $SOURCE_DIR/vgop-service"
    fi
    
    # 检查pom.xml文件
    if [ ! -f "$SOURCE_DIR/vgop-service/pom.xml" ]; then
        fatal_error "pom.xml文件不存在: $SOURCE_DIR/vgop-service/pom.xml"
    fi
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        fatal_error "Java命令未找到，请检查Java是否正确安装"
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        fatal_error "Maven命令未找到，请检查Maven是否正确安装"
    fi
    
    success "环境检查通过"
}

# ================================
# 停止现有服务
# ================================
stop_service() {
    info "检查并停止现有服务..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            info "停止现有服务 (PID: $PID)..."
            kill $PID
            
            # 等待进程停止
            for i in {1..30}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    success "服务已停止"
                    break
                fi
                sleep 1
            done
            
            # 如果进程还在运行，强制杀死
            if ps -p $PID > /dev/null 2>&1; then
                warn "正常停止失败，强制停止服务..."
                kill -9 $PID
            fi
        fi
        rm -f "$PID_FILE"
    else
        info "未发现运行中的服务"
    fi
    
    # 检查端口是否被占用
    if command -v lsof >/dev/null && lsof -Pi :$SERVICE_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        warn "端口 $SERVICE_PORT 仍被占用，尝试释放..."
        lsof -ti:$SERVICE_PORT | xargs kill -9 2>/dev/null || true
    fi
}

# ================================
# 更新代码（可选）
# ================================
update_code() {
    if [ "${UPDATE_CODE:-true}" = "true" ]; then
        info "更新源代码..."
        cd "$SOURCE_DIR"
        
        if [ -d ".git" ]; then
            # 保存当前更改（如果有）
            git stash push -m "Auto stash before deployment $(date)" 2>/dev/null || true
            
            # 拉取最新代码
            git fetch origin
            git reset --hard origin/$GIT_BRANCH
            git clean -fd
            success "代码更新完成"
        else
            warn "不是Git仓库，跳过代码更新"
        fi
    else
        info "跳过代码更新（使用现有代码）"
    fi
}

# ================================
# 构建项目
# ================================
build_project() {
    info "开始构建项目..."
    
    cd "$SOURCE_DIR/vgop-service"
    
    # 清理之前的构建
    mvn clean
    
    # 构建项目，完全跳过测试（编译、运行、集成测试等）
    # 检查profile是否存在，如果不存在就不使用profile
    if mvn help:all-profiles | grep -q "Profile Id: $PROFILE"; then
        info "使用profile: $PROFILE"
        mvn package -DskipTests -Dmaven.test.skip=true -Dmaven.test.skip.exec=true -P$PROFILE
    else
        warn "Profile '$PROFILE' 不存在，使用默认配置构建"
        mvn package -DskipTests -Dmaven.test.skip=true -Dmaven.test.skip.exec=true
    fi
    
    BUILD_RESULT=$?
    if [ $BUILD_RESULT -eq 0 ]; then
        success "项目构建成功"
    else
        error "项目构建失败，退出码: $BUILD_RESULT"
        return 1
    fi
    
    # 检查JAR文件是否生成 - 智能查找JAR文件
    ACTUAL_JAR=$(find target -name "*.jar" -not -name "*sources*" -not -name "*javadoc*" | head -1)
    if [ -n "$ACTUAL_JAR" ]; then
        JAR_NAME=$(basename "$ACTUAL_JAR")
        success "找到JAR文件: $JAR_NAME"
    else
        error "未找到任何JAR文件在target目录中"
        info "target目录内容:"
        ls -la target/ 2>/dev/null || echo "target目录不存在或无法访问"
        return 1
    fi
}

# ================================
# 准备运行环境
# ================================
prepare_runtime() {
    info "准备运行环境..."
    
    # 创建服务目录
    mkdir -p "$SERVICE_DIR"
    mkdir -p "$SERVICE_DIR/logs"
    mkdir -p "$SERVICE_DIR/data"
    mkdir -p "$SERVICE_DIR/config"
    
    # 复制JAR文件
    cp "$SOURCE_DIR/vgop-service/target/$JAR_NAME" "$SERVICE_DIR/"
    
    # 复制配置文件（如果需要外部配置）
    if [ -d "$SOURCE_DIR/vgop-service/src/main/resources" ]; then
        cp -r "$SOURCE_DIR/vgop-service/src/main/resources/"* "$SERVICE_DIR/config/" 2>/dev/null || true
    fi
    
    success "运行环境准备完成"
}

# ================================
# 启动服务
# ================================
start_service() {
    info "启动服务..."
    
    cd "$SERVICE_DIR"
    
    # 构建启动命令
    if [ -n "$JAVA_HOME" ] && [ -x "$JAVA_HOME/bin/java" ]; then
        JAVA_CMD="$JAVA_HOME/bin/java"
    else
        JAVA_CMD="java"
    fi
    
    SPRING_OPTS="--spring.profiles.active=$PROFILE --server.port=$SERVICE_PORT"
    
    # 启动服务
    nohup $JAVA_CMD $JVM_OPTS -jar "$JAR_NAME" $SPRING_OPTS > "$LOG_FILE" 2>&1 &
    
    # 记录PID
    echo $! > "$PID_FILE"
    
    # 等待服务启动
    info "等待服务启动..."
    sleep 10
    
    # 检查服务是否启动成功
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        # 检查端口是否监听
        for i in {1..30}; do
            if command -v lsof >/dev/null && lsof -Pi :$SERVICE_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
                success "服务启动成功！"
                success "PID: $PID"
                success "端口: $SERVICE_PORT"
                success "访问地址: http://localhost:$SERVICE_PORT/vgop"
                success "日志文件: $SERVICE_DIR/$LOG_FILE"
                return 0
            elif command -v netstat >/dev/null && netstat -ln | grep ":$SERVICE_PORT " >/dev/null 2>&1; then
                success "服务启动成功！"
                success "PID: $PID"
                success "端口: $SERVICE_PORT"
                success "访问地址: http://localhost:$SERVICE_PORT/vgop"
                success "日志文件: $SERVICE_DIR/$LOG_FILE"
                return 0
            fi
            sleep 2
        done
        warn "服务进程已启动，但端口 $SERVICE_PORT 未监听，请检查日志"
    else
        error "服务启动失败，请检查日志: $SERVICE_DIR/$LOG_FILE"
    fi
}

# ================================
# 检查服务状态
# ================================
check_status() {
    info "检查服务状态..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            success "服务正在运行 (PID: $PID)"
            if command -v lsof >/dev/null && lsof -Pi :$SERVICE_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
                success "端口 $SERVICE_PORT 正在监听"
            elif command -v netstat >/dev/null && netstat -ln | grep ":$SERVICE_PORT " >/dev/null 2>&1; then
                success "端口 $SERVICE_PORT 正在监听"
            else
                warn "端口 $SERVICE_PORT 未监听"
            fi
        else
            warn "PID文件存在但进程未运行"
        fi
    else
        warn "服务未运行"
    fi
}

# ================================
# 显示日志
# ================================
show_logs() {
    if [ -f "$SERVICE_DIR/$LOG_FILE" ]; then
        info "显示最近50行日志："
        tail -n 50 "$SERVICE_DIR/$LOG_FILE"
    else
        warn "日志文件不存在: $SERVICE_DIR/$LOG_FILE"
    fi
}

# ================================
# 主函数
# ================================
main() {
    info "开始VGOP本地代码部署..."
    info "源代码目录: $SOURCE_DIR"
    info "部署目录: $DEPLOY_DIR"
    info "服务目录: $SERVICE_DIR"
    info "运行环境: $PROFILE"
    
    check_environment
    stop_service
    update_code
    
    # 构建项目，如果失败则退出
    if ! build_project; then
        error "构建失败，部署中止"
        return 1
    fi
    
    prepare_runtime
    start_service
    check_status
    
    success "部署完成！"
    info "可以使用以下命令："
    info "  查看状态: $0 status"
    info "  查看日志: $0 logs"
    info "  停止服务: $0 stop"
}

# ================================
# 命令行参数处理
# ================================
case "${1:-deploy}" in
    "deploy"|"start")
        main
        ;;
    "stop")
        stop_service
        ;;
    "restart")
        stop_service
        sleep 5
        start_service
        check_status
        ;;
    "status")
        check_status
        ;;
    "logs")
        show_logs
        ;;
    "build")
        check_environment
        update_code
        build_project
        ;;
    "update")
        check_environment
        update_code
        ;;
    "no-update")
        UPDATE_CODE=false
        main
        ;;
    *)
        echo "用法: $0 {deploy|start|stop|restart|status|logs|build|update|no-update}"
        echo ""
        echo "命令说明："
        echo "  deploy/start  - 完整部署（更新代码、构建、启动）"
        echo "  stop          - 停止服务"
        echo "  restart       - 重启服务"
        echo "  status        - 查看服务状态"
        echo "  logs          - 查看服务日志"
        echo "  build         - 仅更新代码和构建"
        echo "  update        - 仅更新代码"
        echo "  no-update     - 部署但不更新代码（使用现有代码）"
        echo ""
        echo "环境变量："
        echo "  UPDATE_CODE=false $0 deploy  - 部署时不更新代码"
        exit 1
        ;;
esac 