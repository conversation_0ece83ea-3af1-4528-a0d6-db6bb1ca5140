# 生产环境配置
spring:
  # 数据源配置 - 生产环境暂时使用GBase数据库（与开发环境相同）
  datasource:
    # 主数据源 - GBase数据库
    primary:
      driver-class-name: com.gbasedbt.jdbc.Driver
      url: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
      username: ${DB_USERNAME:ismp}
      password: ${DB_PASSWORD:1qaz@WSX}
      # Druid连接池配置
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 10
        min-idle: 10
        max-active: 50
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM systables WHERE tabid = 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall
    
    # 次数据源 - 使用相同的GBase数据库
    secondary:
      driver-class-name: com.gbasedbt.jdbc.Driver
      url: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
      username: ${ALERT_DB_USERNAME:ismp}
      password: ${ALERT_DB_PASSWORD:1qaz@WSX}
      # Druid连接池配置
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM systables WHERE tabid = 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 10
        filters: stat,wall

# 生产环境日志级别
logging:
  level:
    root: WARN
    com.vgop.service: INFO
    org.springframework: WARN
    org.mybatis: WARN

# 生产环境应用配置
app:
  # 生产环境路径配置
  base-path:
    export-root: "./data/VGOPdata/datafile/"
    log-root: "./logs/"
    backup-root: "./data/backup/vgop/"
  
  # 生产环境SFTP配置
  sftp:
    host: "************"
    port: 22
    username: "bms"
    password: ${SFTP_PASSWORD:EBUPT_b123$}
    remote-base-path: "/home/<USER>/executor/log/image/VGOPdata/datafile/"
    connection-timeout: 30000
    retry-times: 3
    max-pool-size: 10
    
  # 生产环境文件处理配置
  file-processing:
    max-rows-per-file: 2000000  # 生产环境200万行
    
  # 生产环境告警配置
  alert:
    storage:
      enable-database: true
      enable-file: false  # 生产环境仅使用数据库
      
    # 生产环境严格遵守上传时限
    upload-deadline:
      daily: 8    # 每日8点前
      monthly: 8  # 每月1日8点前
      
  # 生产环境备份配置
  backup:
    retention-days: 7
    enabled: true

  # 生产环境任务配置
  tasks:
    # 日常任务列表
    daily:
      # 任务: 24201 - 按照VGOP1-R2.10-24201day.sh脚本配置的主号用户信息查询
      - interfaceId: "24201"
        interfaceName: "VGOP1-R2.10-24201"
        enabled: true
        taskType: "daily"
        export:
          sqlTemplate: "SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '{starttime}' AND mum.openingtime < '{endtime}' AND mum.phonestate IN ('0','1')"
          tempFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24201.unl"
          outputFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}.verf"

      # 任务: 24202 - 接入用户信息查询
      - interfaceId: "24202"
        interfaceName: "VGOP1-R2.10-24202"
        enabled: true
        taskType: "daily"
        export:
          sqlTemplate: "SELECT phonenumber, phonestate, phoneimsi, phoneimei FROM mcn_user_major WHERE openingtime >= '{starttime}' AND openingtime < '{endtime}' AND phonestate = '1'"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202_{revTimes}.verf"

    # 月度任务列表
    monthly:
      # 月度统计任务示例
      - interfaceId: "M24101"
        interfaceName: "VGOP1-R2.11-M24101-MONTHLY"
        enabled: true
        taskType: "monthly"
        export:
          sqlTemplate: "SELECT COUNT(*) as total_count FROM mcn_user_major WHERE phonestate IN ('0','1') AND openingtime >= '{starttime}' AND openingtime < '{endtime}'"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-M24101.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-M24101_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-M24101_{revTimes}.verf"

# 生产环境actuator配置（仅暴露必要端点）
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized

# 生产环境安全配置
server:
  port: 8080
  servlet:
    context-path: /vgop
  # 生产环境使用更多的线程
  undertow:
    threads:
      io: 16
      worker: 128
    buffer-size: 1024
    direct-buffers: true 